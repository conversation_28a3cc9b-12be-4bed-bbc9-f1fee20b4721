<div id="smart-seat-system">
    <style>
        /* 重置样式，防止外部干扰 */
        #smart-seat-system {
            all: initial;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 600px;
            padding: 20px;
            box-sizing: border-box;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            /* 轻量级隔离，确保功能正常 */
            display: block;
        }
        
        #smart-seat-system * {
            box-sizing: border-box;
        }
        
        /* 主容器 */
        #smart-seat-system .seat-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* 头部 */
        #smart-seat-system .seat-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        #smart-seat-system .seat-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        #smart-seat-system .seat-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        /* 黑板和讲台 */
        #smart-seat-system .classroom-front {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        #smart-seat-system .blackboard {
            background: #2c3e50;
            padding: 15px;
            border-radius: 8px;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        #smart-seat-system .podium {
            background: #3498db;
            padding: 12px 30px;
            border-radius: 20px;
            display: inline-block;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        /* 主要内容区 */
        #smart-seat-system .seat-main {
            padding: 30px;
            background: #f8fafc;
        }
        
        /* 控制按钮 */
        #smart-seat-system .seat-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        #smart-seat-system .seat-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            outline: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        #smart-seat-system .seat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        #smart-seat-system .seat-btn:active {
            transform: translateY(0);
        }
        
        #smart-seat-system .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        #smart-seat-system .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        
        #smart-seat-system .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        #smart-seat-system .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }
        
        #smart-seat-system .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        /* 统计信息 */
        #smart-seat-system .seat-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        #smart-seat-system .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        #smart-seat-system .stat-card:hover {
            border-color: #667eea;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        #smart-seat-system .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        #smart-seat-system .stat-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }
        
        /* 座位网格 */
        #smart-seat-system .seat-grid {
            display: grid;
            gap: 16px;
            justify-content: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        /* 单个座位 */
        #smart-seat-system .seat {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            overflow: hidden;
        }
        
        #smart-seat-system .seat:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        #smart-seat-system .seat.empty {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            color: #6c757d;
        }
        
        #smart-seat-system .seat.occupied {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-color: #ff6b9d;
            color: #333;
        }
        
        #smart-seat-system .seat.occupied.male {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-color: #4ecdc4;
        }
        
        #smart-seat-system .seat.occupied.female {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            border-color: #ff8a80;
        }
        
        #smart-seat-system .seat.exceptional {
            border-color: #dc3545 !important;
            border-width: 3px !important;
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3) !important;
        }
        
        #smart-seat-system .seat.highlighted {
            border-color: #007bff !important;
            border-width: 3px !important;
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3) !important;
            z-index: 10;
        }
        
        /* 学生头像 */
        #smart-seat-system .student-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        #smart-seat-system .student-avatar.exceptional {
            background: #dc3545;
            animation: smart-seat-pulse 2s infinite;
        }
        
        @keyframes smart-seat-pulse {
            0% { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); }
            50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4); }
            100% { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); }
        }
        
        /* 学生信息 */
        #smart-seat-system .student-name {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 2px;
        }
        
        #smart-seat-system .student-info {
            font-size: 11px;
            color: #666;
            text-align: center;
            line-height: 1.2;
        }
        
        /* 空座位图标 */
        #smart-seat-system .empty-seat-icon {
            font-size: 32px;
            color: #6c757d;
            margin-bottom: 8px;
            opacity: 0.7;
        }
        
        /* 指示器 */
        #smart-seat-system .indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            cursor: help;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        #smart-seat-system .group-indicator {
            top: 5px;
            left: 5px;
            background: #28a745;
        }
        
        #smart-seat-system .exceptional-indicator {
            top: 5px;
            right: 5px;
            background: #dc3545;
        }
        
        #smart-seat-system .status-indicator {
            bottom: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            border: 1px solid white;
        }
        
        #smart-seat-system .status-online { background: #28a745; }
        #smart-seat-system .status-offline { background: #dc3545; }
        #smart-seat-system .status-away { background: #ffc107; }
        
        /* 错误信息 */
        #smart-seat-system .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        
        #smart-seat-system .error-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        /* 模态框 */
        #smart-seat-system .modal {
            display: none;
            position: fixed;
            z-index: 999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        #smart-seat-system .modal-content {
            background: white;
            margin: 50px auto;
            padding: 30px;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: smart-seat-modalSlideIn 0.3s ease;
        }
        
        @keyframes smart-seat-modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        #smart-seat-system .modal-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f5;
        }
        
        #smart-seat-system .modal-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 15px;
        }
        
        #smart-seat-system .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }
        
        #smart-seat-system .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        #smart-seat-system .modal-close:hover {
            color: #333;
        }
        
        #smart-seat-system .info-section {
            margin-bottom: 25px;
        }
        
        #smart-seat-system .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        #smart-seat-system .info-content {
            color: #666;
            line-height: 1.6;
        }
        
        #smart-seat-system .info-item {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        #smart-seat-system .info-item:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            #smart-seat-system {
                padding: 10px;
            }
            
            #smart-seat-system .seat-container {
                margin: 0;
                border-radius: 12px;
            }
            
            #smart-seat-system .seat-main {
                padding: 20px;
            }
            
            #smart-seat-system .seat {
                width: 100px;
                height: 100px;
            }
            
            #smart-seat-system .seat-controls {
                flex-direction: column;
                align-items: center;
            }
            
            #smart-seat-system .seat-btn {
                width: 100%;
                max-width: 200px;
            }
            
            #smart-seat-system .seat-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            #smart-seat-system .modal-content {
                margin: 20px;
                padding: 20px;
            }
        }
        
        /* 加载动画 */
        #smart-seat-system .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        #smart-seat-system .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: smart-seat-spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes smart-seat-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <div class="seat-container">
        <div class="seat-header">
            <div class="seat-title">🎓 智能排座系统</div>
            <div class="seat-subtitle">AI驱动的智能座位安排，让每个学生都能找到最适合的位置</div>
        </div>
        
        <div class="classroom-front">
            <div class="blackboard">📚 黑板</div>
            <div class="podium">🎯 讲台</div>
        </div>
        
        <div class="seat-main">
            <div class="seat-controls">
                <button class="seat-btn btn-info" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.showInfo() : window.handleSmartSeatAction('showInfo')">📖 使用说明</button>
                <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.shuffleSeats() : window.handleSmartSeatAction('shuffleSeats')">🔄 重新排座</button>
                <button class="seat-btn btn-success" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.highlightGroup() : window.handleSmartSeatAction('highlightGroup')">👥 高亮分组</button>
                <button class="seat-btn btn-danger" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.checkSmartSeatHealth ? window.checkSmartSeatHealth() : alert('系统诊断功能不可用')" style="font-size: 12px;">🩺 系统诊断</button>
            </div>
            
            <div class="seat-stats">
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-totalStudents">-</div>
                    <div class="stat-label">总学生数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-occupiedSeats">-</div>
                    <div class="stat-label">已占座位</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-emptySeats">-</div>
                    <div class="stat-label">空座位</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-dataStatus">等待中</div>
                    <div class="stat-label">数据状态</div>
                </div>
            </div>
            
            <div class="seat-grid" id="smart-seat-seatGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <div>正在加载学生数据...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用说明模态框 -->
    <div id="smart-seat-infoModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.closeModal() : window.handleSmartSeatAction('closeModal')">&times;</span>
            <div class="modal-header">
                <div class="modal-icon">📚</div>
                <div class="modal-title">智能排座系统使用指南</div>
            </div>
            
            <div class="info-section">
                <div class="info-title">🎯 系统特色</div>
                <div class="info-content">
                    <div class="info-item">AI智能算法，根据学生性别、成绩、性格进行最优搭配</div>
                    <div class="info-item">支持特殊学生（视力差、注意力不集中）优先安排</div>
                    <div class="info-item">自动分组功能，促进学生协作学习</div>
                    <div class="info-item">实时数据更新，支持动态调整</div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-title">🔧 功能说明</div>
                <div class="info-content">
                    <div class="info-item"><strong>重新排座：</strong>保持分组关系，随机调整座位顺序</div>
                    <div class="info-item"><strong>高亮分组：</strong>随机选择一个学习小组进行高亮显示</div>
                    <div class="info-item"><strong>系统诊断：</strong>检查系统健康状态和功能可用性</div>
                    <div class="info-item"><strong>点击座位：</strong>查看学生详细信息</div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-title">💡 座位说明</div>
                <div class="info-content">
                    <div class="info-item">🔵 蓝色边框：男学生座位</div>
                    <div class="info-item">🟠 橙色边框：女学生座位</div>
                    <div class="info-item">🔴 红色边框：特殊学生（需要特别关注）</div>
                    <div class="info-item">🟢 绿色标记：学习小组标识</div>
                </div>
            </div>
            
            <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.closeModal() : window.handleSmartSeatAction('closeModal')" style="margin-top: 20px; width: 100%;">
                知道了
            </button>
        </div>
    </div>
    
    <script>
        // 增强的Vue兼容性保护 - 防止DOM操作冲突
        window.vueCompatibleDomOperation = function(operation) {
            return new Promise((resolve, reject) => {
                try {
                    // 多重延迟策略，确保Vue完成所有渲染
                    const executeOperation = () => {
                        try {
                            // 检查DOM是否稳定
                            const container = document.getElementById('smart-seat-system');
                            if (!container || !document.contains(container)) {
                                resolve(null);
                                return;
                            }
                            
                            const result = operation();
                            resolve(result);
                        } catch (error) {
                            // 如果失败，等待更长时间再试
                            setTimeout(() => {
                                try {
                                    const result = operation();
                                    resolve(result);
                                } catch (retryError) {
                                    reject(retryError);
                                }
                            }, 100);
                        }
                    };
                    
                    // 使用requestAnimationFrame确保在下一帧执行
                    if (typeof requestAnimationFrame !== 'undefined') {
                        requestAnimationFrame(() => {
                            setTimeout(executeOperation, 10);
                        });
                    } else {
                        setTimeout(executeOperation, 50);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        };
        
        // 确保全局函数始终可用的保护机制
        window.ensureGlobalFunctions = function() {
            // 重新声明关键的全局函数
            if (!window.vueCompatibleDomOperation) {
                window.vueCompatibleDomOperation = function(op) {
                    return new Promise(resolve => {
                        setTimeout(() => { try { resolve(op()); } catch(e) { resolve(null); } }, 50);
                    });
                };
            }
            
                         if (!window.handleSmartSeatAction) {
                 window.handleSmartSeatAction = function(action) {
                     setTimeout(() => {
                         if (window.SmartSeatSystem && typeof window.SmartSeatSystem[action] === 'function') {
                             try {
                                 window.SmartSeatSystem[action]();
                             } catch (error) {
                                 alert('功能暂时不可用，请稍后重试');
                             }
                         } else {
                             alert('系统正在初始化，请稍后重试');
                         }
                     }, 100);
                 };
            }
            
            
            
            if (!window.checkSmartSeatHealth) {
                window.checkSmartSeatHealth = function() {
                    alert('系统诊断功能正在初始化，请稍后重试');
                };
            }
        };
        
        // 立即确保函数可用
        window.ensureGlobalFunctions();

        // 全局错误处理和备用函数
        window.handleSmartSeatAction = function(action) {
            window.vueCompatibleDomOperation(() => {
                if (window.SmartSeatSystem && typeof window.SmartSeatSystem[action] === 'function') {
                    window.SmartSeatSystem[action]();
                } else {
                    console.error(`❌ SmartSeatSystem.${action} 方法不存在`);
                    alert(`功能暂时不可用，请刷新页面重试。错误：${action} 方法未找到`);
                }
            }).catch(error => {
                console.error(`❌ 执行 ${action} 时出错:`, error);
                alert(`执行功能时出错：${error.message}`);
            });
        };
        
                 // 系统健康检查
         window.checkSmartSeatHealth = function() {
             const hasSystem = !!window.SmartSeatSystem;
             const hasContainer = !!document.getElementById('smart-seat-system');
             const ready = hasSystem && hasContainer;
             
             if (ready) {
                 alert('✅ 智能排座系统运行正常！');
             } else {
                 const issues = [];
                 if (!hasSystem) issues.push('主系统对象缺失');
                 if (!hasContainer) issues.push('容器元素缺失');
                 alert('⚠️ 检测到问题：\n' + issues.join('\n'));
             }
             return { ready, hasSystem, hasContainer };
         };
        
        // 智能座位系统 - 嵌入式版本
        (function() {
            'use strict';
            
            // 系统配置
            const config = {
                rows: 6,
                columns: 6,
                animationDuration: 300,
                highlightDuration: 3000,
                loadTimeout: 5000 // 5秒后自动加载示例数据
            };
            
            // 数据存储
            let studentsData = [];
            let groupsData = [];
            let currentHighlighted = null;
            let isDataLoaded = false;
            let loadAttempts = 0;
            let loadTimeout = null;
            

            
            // 主要功能对象
            const SmartSeatSystem = {
                // 初始化系统
                init() {
                    this.setupEventListeners();
                    this.loadData();
                    this.renderSeats();
                    this.setupEventIsolation();
                    
                    // 设置超时，如果5秒后还没加载到数据，自动显示错误信息
                    loadTimeout = setTimeout(() => {
                        if (!isDataLoaded) {
                            this.showLoadError();
                        }
                    }, config.loadTimeout);
                },
                
                // 加载数据
                loadData() {
                    loadAttempts++;
                    
                    try {
                        // 获取后端数据
                        const rawData = '{{自定义函数.result}}';
                        const rowsData = '{{表单收集.line}}';
                        const columnsData = '{{表单收集.column}}';
                        
                        // 解析行列数据
                        if (rowsData && rowsData !== '{{表单收集.line}}' && !isNaN(parseInt(rowsData))) {
                            config.rows = parseInt(rowsData);
                        }
                        if (columnsData && columnsData !== '{{表单收集.column}}' && !isNaN(parseInt(columnsData))) {
                            config.columns = parseInt(columnsData);
                        }
                        
                        // 检查是否有真实数据
                        if (rawData && rawData !== '{{自定义函数.result}}' && rawData.trim() && rawData.trim() !== '[]') {
                            this.parseData(rawData);
                        } else {
                            // 如果没有真实数据，等待一会再试
                            if (loadAttempts < 3) {
                                setTimeout(() => this.loadData(), 1000);
                            }
                        }
                        
                    } catch (error) {
                        this.showLoadError();
                    }
                },
                
                // 解析数据
                parseData(rawData) {
                    try {
                        const data = JSON.parse(rawData);
                        
                        if (data.sorted_list && Array.isArray(data.sorted_list)) {
                            studentsData = data.sorted_list.map(student => {
                                // 统一字段名映射
                                const name = student.name || student.名称;
                                const genderRaw = student.gender || student.性别;
                                // 统一性别格式为英文
                                const gender = genderRaw === '男' ? 'male' : 
                                              genderRaw === '女' ? 'female' : 
                                              genderRaw === 'male' ? 'male' : 
                                              genderRaw === 'female' ? 'female' : 'male';
                                const score = student.score || student.分数;
                                const character = student.character || student.角色 || [];
                                const exceptional = student.exceptional_student || false;
                                
                                return {
                                    name,
                                    gender,
                                    genderDisplay: genderRaw === '男' || genderRaw === 'male' ? '男' : '女',
                                    score,
                                    character,
                                    exceptional
                                };
                            });
                            
                            // 处理分组数据，支持中文和英文字段名
                            groupsData = data.groups || data.群组 || [];
                            isDataLoaded = true;
                            
                            // 清除超时
                            if (loadTimeout) {
                                clearTimeout(loadTimeout);
                                loadTimeout = null;
                            }
                            
                            this.renderSeats();
                        } else {
                            throw new Error('数据格式不正确：缺少 sorted_list 字段');
                        }
                        
                    } catch (error) {
                        this.showLoadError();
                    }
                },
                
                // 显示加载错误 - Vue兼容版本
                showLoadError() {
                    window.vueCompatibleDomOperation(() => {
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (!grid) {
                            return;
                        }
                        
                        try {
                            grid.innerHTML = `
                                <div class="error-message">
                                    <h3>⚠️ 数据加载失败</h3>
                                    <p>未能获取到学生数据，可能的原因：</p>
                                    <ul>
                                        <li>后端数据尚未准备好</li>
                                        <li>数据格式不正确</li>
                                        <li>网络连接问题</li>
                                        <li>Vue渲染冲突</li>
                                    </ul>
                                    <div class="error-actions">
                                        <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.retryLoad() : window.handleSmartSeatAction('retryLoad')">🔄 重新加载</button>
                                        <button class="seat-btn btn-warning" onclick="location.reload()">🔄 刷新页面</button>
                                    </div>
                                </div>
                            `;
                        } catch (error) {
                            // 静默处理错误
                        }
                        
                        this.updateStats();
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 重新加载数据 - Vue兼容版本
                retryLoad() {
                    isDataLoaded = false;
                    loadAttempts = 0;
                    studentsData = [];
                    groupsData = [];
                    
                    window.vueCompatibleDomOperation(() => {
                        // 显示加载状态
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (grid) {
                            try {
                                grid.innerHTML = `
                                    <div class="loading">
                                        <div class="loading-spinner"></div>
                                        <div>正在重新加载数据...</div>
                                    </div>
                                `;
                            } catch (error) {
                                // 静默处理错误
                            }
                        }
                        
                        this.loadData();
                    }).catch(error => {
                        this.loadData(); // 尝试直接加载数据
                    });
                },
                


                
                // 安全的DOM操作 - Vue兼容
                safeGetElement(id) {
                    try {
                        const element = document.getElementById(id);
                        if (!element || !document.contains(element)) {
                            return null;
                        }
                        return element;
                    } catch (error) {
                        return null;
                    }
                },

                // 安全的DOM操作
                safeQuerySelectorAll(parent, selector) {
                    try {
                        if (!parent || !document.contains(parent)) {
                            return [];
                        }
                        return Array.from(parent.querySelectorAll(selector) || []);
                    } catch (error) {
                        return [];
                    }
                },

                // 渲染座位 - Vue兼容版本
                renderSeats() {
                    return window.vueCompatibleDomOperation(() => {
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (!grid) {
                            return;
                        }
                        
                        const totalSeats = config.rows * config.columns;
                        
                        try {
                            // 设置网格样式
                            grid.style.gridTemplateColumns = `repeat(${config.columns}, 1fr)`;
                            grid.style.gridTemplateRows = `repeat(${config.rows}, 1fr)`;
                            
                            // 最安全的清空方式 - 避免Vue冲突
                            try {
                                // 方法1：尝试使用innerHTML（最安全）
                                grid.innerHTML = '';
                            } catch (htmlError) {
                                // 方法2：如果innerHTML失败，尝试安全移除子元素
                                const children = Array.from(grid.children || []);
                                children.forEach(child => {
                                    try {
                                        if (child.parentNode === grid) {
                                            grid.removeChild(child);
                                        }
                                    } catch (removeError) {
                                        // 静默处理错误
                                    }
                                });
                            }
                            
                            // 如果没有数据，显示加载状态
                            if (!isDataLoaded || studentsData.length === 0) {
                                for (let i = 0; i < totalSeats; i++) {
                                    const seat = this.createEmptySeat();
                                    if (seat && document.contains(grid)) {
                                        grid.appendChild(seat);
                                    }
                                }
                                this.updateStats();
                                return;
                            }
                            
                            // 按分组排列学生
                            const arrangedStudents = this.arrangeStudentsByGroups();
                            
                            // 生成座位
                            for (let i = 0; i < totalSeats; i++) {
                                const seat = i < arrangedStudents.length 
                                    ? this.createStudentSeat(arrangedStudents[i], i)
                                    : this.createEmptySeat();
                                if (seat && document.contains(grid)) {
                                    grid.appendChild(seat);
                                }
                            }
                            
                            this.updateStats();
                        } catch (error) {
                            // 降级处理：使用innerHTML
                            grid.innerHTML = `
                                <div class="error-message">
                                    <h3>⚠️ 渲染出错</h3>
                                    <p>座位渲染时遇到问题，请刷新页面重试</p>
                                    <button class="seat-btn btn-primary" onclick="location.reload()">🔄 刷新页面</button>
                                </div>
                            `;
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 创建空座位
                createEmptySeat() {
                    const seat = document.createElement('div');
                    seat.className = 'seat empty';
                    seat.innerHTML = `
                        <div class="empty-seat-icon">🪑</div>
                        <div class="student-name">${isDataLoaded ? '空座位' : '等待数据'}</div>
                    `;
                    return seat;
                },
                
                // 创建学生座位
                createStudentSeat(student, index) {
                    const seat = document.createElement('div');
                    seat.className = `seat occupied ${student.gender}`;
                    seat.dataset.index = index;
                    seat.dataset.studentName = student.name;
                    
                    if (student.exceptional) {
                        seat.classList.add('exceptional');
                    }
                    
                    // 获取分组信息
                    const groupInfo = this.getGroupInfo(student.name);
                    const characterText = Array.isArray(student.character) 
                        ? student.character.slice(0, 2).join(' • ')
                        : student.character || '普通';
                    
                    seat.innerHTML = `
                        <div class="student-avatar ${student.exceptional ? 'exceptional' : ''}">${student.name.charAt(0)}</div>
                        <div class="student-name">${student.name}</div>
                        <div class="student-info">${student.score}分 • ${characterText}</div>
                        <div class="status-indicator status-online" title="在线"></div>
                        ${groupInfo ? `<div class="indicator group-indicator" title="分组: ${groupInfo}">👥</div>` : ''}
                        ${student.exceptional ? `<div class="indicator exceptional-indicator" title="特殊学生">⚠️</div>` : ''}
                    `;
                    
                    // 添加点击事件
                    seat.addEventListener('click', () => this.showStudentDetails(student));
                    
                    return seat;
                },
                
                // 按分组排列学生
                arrangeStudentsByGroups() {
                    const arranged = [];
                    const used = new Set();
                    
                    // 优先放置分组学生
                    for (const group of groupsData) {
                        for (const name of group) {
                            const student = studentsData.find(s => s.name === name);
                            if (student && !used.has(name)) {
                                arranged.push(student);
                                used.add(name);
                            }
                        }
                    }
                    
                    // 添加未分组学生
                    for (const student of studentsData) {
                        if (!used.has(student.name)) {
                            arranged.push(student);
                            used.add(student.name);
                        }
                    }
                    
                    return arranged;
                },
                
                // 获取分组信息
                getGroupInfo(studentName) {
                    for (const group of groupsData) {
                        if (group.includes(studentName)) {
                            return group.filter(name => name !== studentName).join(', ');
                        }
                    }
                    return null;
                },
                
                // 显示学生详情
                showStudentDetails(student) {
                    const groupInfo = this.getGroupInfo(student.name);
                    const characterText = Array.isArray(student.character) 
                        ? student.character.join(', ')
                        : student.character || '普通';
                    
                    const genderDisplay = student.genderDisplay || student.gender;
                    
                    const message = `
👤 学生信息详情

📝 姓名：${student.name}
${genderDisplay === '男' ? '👨' : '👩'} 性别：${genderDisplay}
📊 成绩：${student.score}分
🎭 性格：${characterText}
${student.exceptional ? '⚠️ 特殊学生：是' : '✅ 特殊学生：否'}
${groupInfo ? `👥 分组伙伴：${groupInfo}` : ''}

${student.exceptional ? '\n💡 建议：请给予特别关注和支持' : ''}
                    `.trim();
                    
                    alert(message);
                },
                
                // 更新统计信息 - Vue兼容版本
                updateStats() {
                    return window.vueCompatibleDomOperation(() => {
                        const totalSeats = config.rows * config.columns;
                        const occupiedSeats = studentsData.length;
                        const emptySeats = totalSeats - occupiedSeats;
                        
                        const elements = {
                            totalStudents: this.safeGetElement('smart-seat-totalStudents'),
                            occupiedSeats: this.safeGetElement('smart-seat-occupiedSeats'),
                            emptySeats: this.safeGetElement('smart-seat-emptySeats'),
                            dataStatus: this.safeGetElement('smart-seat-dataStatus')
                        };
                        
                        try {
                            if (elements.totalStudents) elements.totalStudents.textContent = studentsData.length;
                            if (elements.occupiedSeats) elements.occupiedSeats.textContent = occupiedSeats;
                            if (elements.emptySeats) elements.emptySeats.textContent = emptySeats;
                            if (elements.dataStatus) {
                                elements.dataStatus.textContent = isDataLoaded ? '已加载' : '等待中';
                            }
                        } catch (error) {
                            // 静默处理错误
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 重新排座
                shuffleSeats() {
                    if (!isDataLoaded) {
                        alert('⚠️ 请先加载数据');
                        return;
                    }
                    
                    if (studentsData.length === 0) {
                        alert('⚠️ 没有学生数据');
                        return;
                    }
                    
                    // 打乱分组顺序
                    for (let i = groupsData.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [groupsData[i], groupsData[j]] = [groupsData[j], groupsData[i]];
                    }
                    
                    this.renderSeats();
                },
                
                // 高亮分组 - Vue兼容版本
                highlightGroup() {
                    if (!isDataLoaded || groupsData.length === 0) {
                        alert('⚠️ 没有可用的分组数据');
                        return;
                    }
                    
                    window.vueCompatibleDomOperation(() => {
                        this.clearHighlights();
                        
                        const randomGroup = groupsData[Math.floor(Math.random() * groupsData.length)];
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        const seats = this.safeQuerySelectorAll(container, '.seat.occupied');
                        
                        seats.forEach(seat => {
                            try {
                                const studentName = seat.dataset.studentName;
                                if (randomGroup.includes(studentName)) {
                                    seat.classList.add('highlighted');
                                }
                            } catch (error) {
                                // 静默处理错误
                            }
                        });
                        
                        alert(`🎯 高亮分组：${randomGroup.join(' 和 ')}\n\n这些学生组成一个学习小组，建议加强协作！`);
                        
                        // 3秒后自动取消高亮
                        setTimeout(() => this.clearHighlights(), config.highlightDuration);
                    }).catch(error => {
                        alert('⚠️ 高亮分组功能暂时不可用');
                    });
                },

                
                // 清除高亮 - Vue兼容版本
                clearHighlights() {
                    try {
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        const highlightedSeats = this.safeQuerySelectorAll(container, '.seat.highlighted');
                        highlightedSeats.forEach(seat => {
                            try {
                                seat.classList.remove('highlighted');
                            } catch (error) {
                                // 静默处理错误
                            }
                        });
                        currentHighlighted = null;
                    } catch (error) {
                        // 静默处理错误
                    }
                },
                
                // 显示使用说明 - Vue兼容版本
                showInfo() {
                    window.vueCompatibleDomOperation(() => {
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.style.display = 'block';
                        } else {
                            alert('使用说明功能暂时不可用，请刷新页面重试');
                        }
                    }).catch(error => {
                        alert('使用说明功能暂时不可用');
                    });
                },
                
                // 关闭模态框 - Vue兼容版本
                closeModal() {
                    window.vueCompatibleDomOperation(() => {
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.style.display = 'none';
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 设置事件监听器 - Vue兼容版本
                setupEventListeners() {
                    window.vueCompatibleDomOperation(() => {
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        // 模态框外部点击关闭
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.addEventListener('click', (e) => {
                                if (e.target.id === 'smart-seat-infoModal') {
                                    this.closeModal();
                                }
                            });
                        }
                        
                        // 键盘事件 - 只处理ESC键关闭模态框
                        document.addEventListener('keydown', (e) => {
                            if (e.key === 'Escape') {
                                // 只有当模态框显示时才关闭
                                const currentModal = this.safeGetElement('smart-seat-infoModal');
                                if (currentModal && currentModal.style.display === 'block') {
                                    this.closeModal();
                                    e.preventDefault(); // 只阻止ESC的默认行为
                                }
                            }
                        });
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 设置事件隔离 - 轻量级保护
                setupEventIsolation() {
                    // 事件保护已在Vue兼容操作中处理
                }
            };
            
                         // 防护措施：确保全局对象正确暴露
             if (typeof window !== 'undefined') {
                 window.SmartSeatSystem = SmartSeatSystem;
             }
            
                         // 初始化函数 - 支持多次调用
             function initializeSystem() {
                 try {
                     SmartSeatSystem.init();
                 } catch (error) {
                     setTimeout(initializeSystem, 1000); // 1秒后重试
                 }
             }
            
            // 多重初始化策略，应对Vue渲染
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeSystem);
            } else {
                // 立即执行
                initializeSystem();
            }
            
                         // 额外的延迟初始化，防止Vue渲染干扰
             setTimeout(() => {
                 if (!window.SmartSeatSystem || typeof window.SmartSeatSystem.showInfo !== 'function') {
                     window.SmartSeatSystem = SmartSeatSystem;
                     initializeSystem();
                 }
             }, 500);
             
             // Vue兼容的DOM变化监控器
             if (typeof MutationObserver !== 'undefined') {
                 const observer = new MutationObserver((mutations) => {
                     mutations.forEach((mutation) => {
                         if (mutation.type === 'childList') {
                             // 检查是否有组件被Vue重新渲染
                             const container = document.getElementById('smart-seat-system');
                             if (container && !container.dataset.vueCompatible) {
                                 console.log('🔄 检测到Vue重新渲染，重新初始化组件...');
                                 container.dataset.vueCompatible = 'true';
                                 
                                 // 确保全局对象可用
                                 window.SmartSeatSystem = SmartSeatSystem;
                                 window.vueCompatibleDomOperation = window.vueCompatibleDomOperation || function(op) {
                                     return new Promise((resolve, reject) => {
                                         setTimeout(() => {
                                             try { resolve(op()); } catch(e) { reject(e); }
                                         }, 0);
                                     });
                                 };
                                 window.handleSmartSeatAction = window.handleSmartSeatAction || function(action) {
                                     console.log('备用处理器:', action);
                                 };
                                 
                                 setTimeout(() => {
                                     try {
                                         initializeSystem();
                                     } catch (error) {
                                         console.error('❌ 重新初始化失败:', error);
                                     }
                                 }, 100);
                             }
                         }
                     });
                 });
                 
                 // 开始观察
                 setTimeout(() => {
                     const targetNode = document.body;
                     if (targetNode) {
                         observer.observe(targetNode, {
                             childList: true,
                             subtree: true,
                             attributes: false,
                             characterData: false
                         });
                         console.log('🔍 Vue兼容的DOM监控器已启动');
                     }
                 }, 1000);
             }
             
             // 增强的全局错误恢复机制
             window.addEventListener('error', (event) => {
                 const errorMessage = event.message || '';
                 if (errorMessage.includes('querySelectorAll') || 
                     errorMessage.includes('removeChild') || 
                     errorMessage.includes('handleSmartSeatAction')) {
                     
                     // 延迟恢复，确保Vue操作完成
                     setTimeout(() => {
                         try {
                             // 重新确保全局函数可用
                             window.ensureGlobalFunctions();
                             
                                                      // 重新设置主系统对象
                         if (window.SmartSeatSystem !== SmartSeatSystem) {
                             window.SmartSeatSystem = SmartSeatSystem;
                         }
                         
                         // 检查容器是否存在，如果不存在说明被Vue破坏了
                         const container = document.getElementById('smart-seat-system');
                         if (container && !container.dataset.errorRecovery) {
                             container.dataset.errorRecovery = 'true';
                         }
                                              } catch (recoveryError) {
                         // 静默处理恢复错误
                     }
                     }, 500);
                 }
             });
             
             // 定期健康检查和自动恢复
             setInterval(() => {
                 try {
                     // 检查关键函数是否存在
                     if (!window.handleSmartSeatAction || !window.vueCompatibleDomOperation) {
                         window.ensureGlobalFunctions();
                     }
                     
                     // 检查主系统对象
                     if (!window.SmartSeatSystem || typeof window.SmartSeatSystem.showInfo !== 'function') {
                         if (typeof SmartSeatSystem !== 'undefined') {
                             window.SmartSeatSystem = SmartSeatSystem;
                         }
                     }
                 } catch (checkError) {
                     // 静默处理错误
                 }
             }, 5000); // 每5秒检查一次
             
             // Vue状态变化监听
             if (typeof MutationObserver !== 'undefined') {
                 const globalObserver = new MutationObserver((mutations) => {
                     let needsRecovery = false;
                     
                     mutations.forEach((mutation) => {
                         if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                             // 检查是否有我们的组件被移除
                             mutation.removedNodes.forEach((node) => {
                                 if (node.nodeType === 1 && (
                                     node.id === 'smart-seat-system' || 
                                     node.querySelector && node.querySelector('#smart-seat-system')
                                 )) {
                                     needsRecovery = true;
                                 }
                             });
                         }
                     });
                     
                     if (needsRecovery) {
                         setTimeout(() => {
                             window.ensureGlobalFunctions();
                             if (typeof SmartSeatSystem !== 'undefined') {
                                 window.SmartSeatSystem = SmartSeatSystem;
                             }
                         }, 1000);
                     }
                 });
                 
                 // 观察整个文档的变化
                 setTimeout(() => {
                     globalObserver.observe(document.body, {
                         childList: true,
                         subtree: true
                     });
                 }, 2000);
             }
            
        })();
    </script>
</div>
