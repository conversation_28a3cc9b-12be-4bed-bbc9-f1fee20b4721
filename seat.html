<div id="smart-seat-system">
    <style>
        /* 重置样式，防止外部干扰 */
        #smart-seat-system {
            all: initial;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 600px;
            padding: 20px;
            box-sizing: border-box;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            /* 轻量级隔离，确保功能正常 */
            display: block;
        }
        
        #smart-seat-system * {
            box-sizing: border-box;
        }
        
        /* 主容器 */
        #smart-seat-system .seat-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* 头部 */
        #smart-seat-system .seat-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        #smart-seat-system .seat-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        #smart-seat-system .seat-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        /* 黑板和讲台 */
        #smart-seat-system .classroom-front {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        #smart-seat-system .blackboard {
            background: #2c3e50;
            padding: 15px;
            border-radius: 8px;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        #smart-seat-system .podium {
            background: #3498db;
            padding: 12px 30px;
            border-radius: 20px;
            display: inline-block;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        /* 主要内容区 */
        #smart-seat-system .seat-main {
            padding: 30px;
            background: #f8fafc;
        }
        
        /* 控制按钮 */
        #smart-seat-system .seat-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        #smart-seat-system .seat-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            outline: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        #smart-seat-system .seat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        #smart-seat-system .seat-btn:active {
            transform: translateY(0);
        }
        
        #smart-seat-system .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        #smart-seat-system .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        
        #smart-seat-system .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        #smart-seat-system .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }
        
        #smart-seat-system .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        /* 统计信息 */
        #smart-seat-system .seat-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        #smart-seat-system .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        #smart-seat-system .stat-card:hover {
            border-color: #667eea;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        #smart-seat-system .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        #smart-seat-system .stat-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }
        
        /* 座位网格 */
        #smart-seat-system .seat-grid {
            display: grid;
            gap: 16px;
            justify-content: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        /* 单个座位 */
        #smart-seat-system .seat {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            overflow: hidden;
        }
        
        #smart-seat-system .seat:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        #smart-seat-system .seat.empty {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            color: #6c757d;
        }
        
        #smart-seat-system .seat.occupied {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-color: #ff6b9d;
            color: #333;
        }
        
        #smart-seat-system .seat.occupied.male {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-color: #4ecdc4;
        }
        
        #smart-seat-system .seat.occupied.female {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            border-color: #ff8a80;
        }
        
        #smart-seat-system .seat.exceptional {
            border-color: #dc3545 !important;
            border-width: 3px !important;
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3) !important;
        }
        
        #smart-seat-system .seat.highlighted {
            border-color: #007bff !important;
            border-width: 3px !important;
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3) !important;
            z-index: 10;
        }
        
        /* 学生头像 */
        #smart-seat-system .student-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        #smart-seat-system .student-avatar.exceptional {
            background: #dc3545;
            animation: smart-seat-pulse 2s infinite;
        }
        
        @keyframes smart-seat-pulse {
            0% { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); }
            50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4); }
            100% { box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); }
        }
        
        /* 学生信息 */
        #smart-seat-system .student-name {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 2px;
        }
        
        #smart-seat-system .student-info {
            font-size: 11px;
            color: #666;
            text-align: center;
            line-height: 1.2;
        }
        
        /* 空座位图标 */
        #smart-seat-system .empty-seat-icon {
            font-size: 32px;
            color: #6c757d;
            margin-bottom: 8px;
            opacity: 0.7;
        }
        
        /* 指示器 */
        #smart-seat-system .indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
            cursor: help;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        #smart-seat-system .group-indicator {
            top: 5px;
            left: 5px;
            background: #28a745;
        }
        
        #smart-seat-system .exceptional-indicator {
            top: 5px;
            right: 5px;
            background: #dc3545;
        }
        
        #smart-seat-system .status-indicator {
            bottom: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            border: 1px solid white;
        }
        
        #smart-seat-system .status-online { background: #28a745; }
        #smart-seat-system .status-offline { background: #dc3545; }
        #smart-seat-system .status-away { background: #ffc107; }
        
        /* 错误信息 */
        #smart-seat-system .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        
        #smart-seat-system .error-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        /* 模态框 */
        #smart-seat-system .modal {
            display: none;
            position: fixed;
            z-index: 999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        #smart-seat-system .modal-content {
            background: white;
            margin: 50px auto;
            padding: 30px;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: smart-seat-modalSlideIn 0.3s ease;
        }
        
        @keyframes smart-seat-modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        #smart-seat-system .modal-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f5;
        }
        
        #smart-seat-system .modal-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 15px;
        }
        
        #smart-seat-system .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }
        
        #smart-seat-system .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        #smart-seat-system .modal-close:hover {
            color: #333;
        }
        
        #smart-seat-system .info-section {
            margin-bottom: 25px;
        }
        
        #smart-seat-system .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        #smart-seat-system .info-content {
            color: #666;
            line-height: 1.6;
        }
        
        #smart-seat-system .info-item {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        #smart-seat-system .info-item:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            #smart-seat-system {
                padding: 10px;
            }
            
            #smart-seat-system .seat-container {
                margin: 0;
                border-radius: 12px;
            }
            
            #smart-seat-system .seat-main {
                padding: 20px;
            }
            
            #smart-seat-system .seat {
                width: 100px;
                height: 100px;
            }
            
            #smart-seat-system .seat-controls {
                flex-direction: column;
                align-items: center;
            }
            
            #smart-seat-system .seat-btn {
                width: 100%;
                max-width: 200px;
            }
            
            #smart-seat-system .seat-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            #smart-seat-system .modal-content {
                margin: 20px;
                padding: 20px;
            }
        }
        
        /* 加载动画 */
        #smart-seat-system .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        #smart-seat-system .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: smart-seat-spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes smart-seat-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <div class="seat-container">
        <div class="seat-header">
            <div class="seat-title">🎓 智能排座系统</div>
            <div class="seat-subtitle">AI驱动的智能座位安排，让每个学生都能找到最适合的位置</div>
        </div>
        
        <div class="classroom-front">
            <div class="blackboard">📚 黑板</div>
            <div class="podium">🎯 讲台</div>
        </div>
        
        <div class="seat-main">
            <div class="seat-controls">
                <button class="seat-btn btn-info" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.showInfo() : window.handleSmartSeatAction('showInfo')">📖 使用说明</button>
                <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.shuffleSeats() : window.handleSmartSeatAction('shuffleSeats')">🔄 重新排座</button>
                <button class="seat-btn btn-success" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.highlightGroup() : window.handleSmartSeatAction('highlightGroup')">👥 高亮分组</button>
                <button class="seat-btn btn-danger" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.checkSmartSeatHealth ? window.checkSmartSeatHealth() : alert('系统诊断功能不可用')" style="font-size: 12px;">🩺 系统诊断</button>
            </div>
            
            <div class="seat-stats">
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-totalStudents">-</div>
                    <div class="stat-label">总学生数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-occupiedSeats">-</div>
                    <div class="stat-label">已占座位</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-emptySeats">-</div>
                    <div class="stat-label">空座位</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="smart-seat-dataStatus">等待中</div>
                    <div class="stat-label">数据状态</div>
                </div>
            </div>
            
            <div class="seat-grid" id="smart-seat-seatGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <div>正在加载学生数据...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用说明模态框 -->
    <div id="smart-seat-infoModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.closeModal() : window.handleSmartSeatAction('closeModal')">&times;</span>
            <div class="modal-header">
                <div class="modal-icon">📚</div>
                <div class="modal-title">智能排座系统使用指南</div>
            </div>
            
            <div class="info-section">
                <div class="info-title">🎯 系统特色</div>
                <div class="info-content">
                    <div class="info-item">AI智能算法，根据学生性别、成绩、性格进行最优搭配</div>
                    <div class="info-item">支持特殊学生（视力差、注意力不集中）优先安排</div>
                    <div class="info-item">自动分组功能，促进学生协作学习</div>
                    <div class="info-item">实时数据更新，支持动态调整</div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-title">🔧 功能说明</div>
                <div class="info-content">
                    <div class="info-item"><strong>重新排座：</strong>保持分组关系，随机调整座位顺序</div>
                    <div class="info-item"><strong>高亮分组：</strong>随机选择一个学习小组进行高亮显示</div>
                    <div class="info-item"><strong>系统诊断：</strong>检查系统健康状态和功能可用性</div>
                    <div class="info-item"><strong>点击座位：</strong>查看学生详细信息</div>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-title">💡 座位说明</div>
                <div class="info-content">
                    <div class="info-item">🔵 蓝色边框：男学生座位</div>
                    <div class="info-item">🟠 橙色边框：女学生座位</div>
                    <div class="info-item">🔴 红色边框：特殊学生（需要特别关注）</div>
                    <div class="info-item">🟢 绿色标记：学习小组标识</div>
                </div>
            </div>
            
            <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.closeModal() : window.handleSmartSeatAction('closeModal')" style="margin-top: 20px; width: 100%;">
                知道了
            </button>
        </div>
    </div>
    
    <script>
        // 增强的Vue兼容性保护 - 防止DOM操作冲突
        window.vueCompatibleDomOperation = function(operation) {
            return new Promise((resolve, reject) => {
                try {
                    // 多重延迟策略，确保Vue完成所有渲染
                    const executeOperation = () => {
                        try {
                            // 检查DOM是否稳定
                            const container = document.getElementById('smart-seat-system');
                            if (!container || !document.contains(container)) {
                                console.warn('⚠️ 容器不存在或已移除，跳过操作');
                                resolve(null);
                                return;
                            }

                            // 检查Vue是否正在进行更新
                            if (window.Vue && window.Vue.nextTick) {
                                window.Vue.nextTick(() => {
                                    try {
                                        const result = operation();
                                        resolve(result);
                                    } catch (error) {
                                        console.warn('⚠️ Vue nextTick 操作失败:', error);
                                        reject(error);
                                    }
                                });
                                return;
                            }

                            const result = operation();
                            resolve(result);
                        } catch (error) {
                            console.warn('⚠️ DOM操作失败，将重试:', error);
                            // 如果失败，等待更长时间再试
                            setTimeout(() => {
                                try {
                                    const result = operation();
                                    resolve(result);
                                } catch (retryError) {
                                    console.error('❌ DOM操作重试后仍失败:', retryError);
                                    reject(retryError);
                                }
                            }, 200);
                        }
                    };

                    // 使用requestAnimationFrame确保在下一帧执行
                    if (typeof requestAnimationFrame !== 'undefined') {
                        requestAnimationFrame(() => {
                            setTimeout(executeOperation, 50);
                        });
                    } else {
                        setTimeout(executeOperation, 100);
                    }
                } catch (error) {
                    console.error('❌ vueCompatibleDomOperation 初始化失败:', error);
                    reject(error);
                }
            });
        };
        
        // 确保全局函数始终可用的保护机制
        window.ensureGlobalFunctions = function() {
            console.log('🔧 确保全局函数可用...');

            // 重新声明关键的全局函数
            if (!window.vueCompatibleDomOperation) {
                window.vueCompatibleDomOperation = function(op) {
                    return new Promise((resolve, reject) => {
                        try {
                            // 检查Vue环境
                            if (window.Vue && window.Vue.nextTick) {
                                window.Vue.nextTick(() => {
                                    try { resolve(op()); } catch(e) { reject(e); }
                                });
                            } else {
                                setTimeout(() => {
                                    try { resolve(op()); } catch(e) { reject(e); }
                                }, 100);
                            }
                        } catch(e) {
                            reject(e);
                        }
                    });
                };
            }

            if (!window.handleSmartSeatAction) {
                window.handleSmartSeatAction = function(action) {
                    console.log('🎯 处理动作:', action);

                    // 使用Vue兼容的操作
                    window.vueCompatibleDomOperation(() => {
                        if (window.SmartSeatSystem && typeof window.SmartSeatSystem[action] === 'function') {
                            try {
                                window.SmartSeatSystem[action]();
                            } catch (error) {
                                console.error('❌ 执行动作失败:', action, error);
                                alert('功能暂时不可用，请稍后重试');
                            }
                        } else {
                            console.warn('⚠️ SmartSeatSystem 或方法不存在:', action);
                            alert('系统正在初始化，请稍后重试');
                        }
                    }).catch(error => {
                        console.error('❌ Vue兼容操作失败:', error);
                        alert('操作失败，请刷新页面重试');
                    });
                };
            }

            if (!window.checkSmartSeatHealth) {
                window.checkSmartSeatHealth = function() {
                    console.log('🩺 执行系统健康检查...');

                    const hasSystem = !!window.SmartSeatSystem;
                    const hasContainer = !!document.getElementById('smart-seat-system');
                    const hasVueCompat = !!window.vueCompatibleDomOperation;
                    const ready = hasSystem && hasContainer && hasVueCompat;

                    if (ready) {
                        alert('✅ 智能排座系统运行正常！\n\n系统对象: ✓\n容器元素: ✓\nVue兼容: ✓');
                    } else {
                        const issues = [];
                        if (!hasSystem) issues.push('❌ 主系统对象缺失');
                        if (!hasContainer) issues.push('❌ 容器元素缺失');
                        if (!hasVueCompat) issues.push('❌ Vue兼容层缺失');
                        alert('⚠️ 检测到问题：\n\n' + issues.join('\n') + '\n\n建议刷新页面重试');
                    }

                    return { ready, hasSystem, hasContainer, hasVueCompat };
                };
            }

            console.log('✅ 全局函数确保完成');
        };
        
        // 立即确保函数可用
        window.ensureGlobalFunctions();

        // 全局错误处理和备用函数 - 增强版
        window.handleSmartSeatAction = function(action) {
            console.log('🎯 全局处理动作:', action);

            // 确保函数可用
            window.ensureGlobalFunctions();

            return window.vueCompatibleDomOperation(() => {
                if (window.SmartSeatSystem && typeof window.SmartSeatSystem[action] === 'function') {
                    console.log('✅ 执行系统方法:', action);
                    return window.SmartSeatSystem[action]();
                } else {
                    console.error(`❌ SmartSeatSystem.${action} 方法不存在`);
                    throw new Error(`方法 ${action} 未找到`);
                }
            }).catch(error => {
                console.error(`❌ 执行 ${action} 时出错:`, error);

                // 尝试恢复
                setTimeout(() => {
                    window.ensureGlobalFunctions();
                    if (window.SmartSeatSystem && typeof window.SmartSeatSystem[action] === 'function') {
                        try {
                            window.SmartSeatSystem[action]();
                        } catch (retryError) {
                            alert(`功能执行失败：${retryError.message}\n\n请刷新页面重试`);
                        }
                    } else {
                        alert(`功能暂时不可用：${action}\n\n请刷新页面重试`);
                    }
                }, 500);
            });
        };

        // 系统健康检查 - 增强版
        window.checkSmartSeatHealth = function() {
            console.log('🩺 执行系统健康检查...');

            const checks = {
                hasSystem: !!window.SmartSeatSystem,
                hasContainer: !!document.getElementById('smart-seat-system'),
                hasVueCompat: !!window.vueCompatibleDomOperation,
                hasHandleAction: !!window.handleSmartSeatAction,
                hasEnsureGlobal: !!window.ensureGlobalFunctions,
                isVueEnvironment: !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('.vue-'))
            };

            const ready = checks.hasSystem && checks.hasContainer && checks.hasVueCompat;

            console.log('🔍 健康检查结果:', checks);

            if (ready) {
                alert('✅ 智能排座系统运行正常！\n\n' +
                      '系统对象: ✓\n' +
                      '容器元素: ✓\n' +
                      'Vue兼容: ✓\n' +
                      '全局函数: ✓\n' +
                      (checks.isVueEnvironment ? 'Vue环境: ✓' : 'Vue环境: ❌'));
            } else {
                const issues = [];
                if (!checks.hasSystem) issues.push('❌ 主系统对象缺失');
                if (!checks.hasContainer) issues.push('❌ 容器元素缺失');
                if (!checks.hasVueCompat) issues.push('❌ Vue兼容层缺失');
                if (!checks.hasHandleAction) issues.push('❌ 动作处理器缺失');
                if (!checks.hasEnsureGlobal) issues.push('❌ 全局函数确保器缺失');

                alert('⚠️ 检测到问题：\n\n' + issues.join('\n') +
                      '\n\nVue环境: ' + (checks.isVueEnvironment ? '✓' : '❌') +
                      '\n\n建议：刷新页面或联系技术支持');
            }

            return checks;
        };
        
        // 智能座位系统 - 嵌入式版本
        (function() {
            'use strict';
            
            // 系统配置
            const config = {
                rows: 6,
                columns: 6,
                animationDuration: 300,
                highlightDuration: 3000,
                loadTimeout: 5000 // 5秒后自动加载示例数据
            };
            
            // 数据存储
            let studentsData = [];
            let groupsData = [];
            let currentHighlighted = null;
            let isDataLoaded = false;
            let loadAttempts = 0;
            let loadTimeout = null;
            

            
            // 主要功能对象
            const SmartSeatSystem = {
                // 初始化系统
                init() {
                    this.setupEventListeners();
                    this.loadData();
                    this.renderSeats();
                    this.setupEventIsolation();
                    
                    // 设置超时，如果5秒后还没加载到数据，自动显示错误信息
                    loadTimeout = setTimeout(() => {
                        if (!isDataLoaded) {
                            this.showLoadError();
                        }
                    }, config.loadTimeout);
                },
                
                // 加载数据
                loadData() {
                    loadAttempts++;
                    
                    try {
                        // 获取后端数据
                        const rawData = '{{自定义函数.result}}';
                        const rowsData = '{{表单收集.line}}';
                        const columnsData = '{{表单收集.column}}';
                        
                        // 解析行列数据
                        if (rowsData && rowsData !== '{{表单收集.line}}' && !isNaN(parseInt(rowsData))) {
                            config.rows = parseInt(rowsData);
                        }
                        if (columnsData && columnsData !== '{{表单收集.column}}' && !isNaN(parseInt(columnsData))) {
                            config.columns = parseInt(columnsData);
                        }
                        
                        // 检查是否有真实数据
                        if (rawData && rawData !== '{{自定义函数.result}}' && rawData.trim() && rawData.trim() !== '[]') {
                            this.parseData(rawData);
                        } else {
                            // 如果没有真实数据，等待一会再试
                            if (loadAttempts < 3) {
                                setTimeout(() => this.loadData(), 1000);
                            }
                        }
                        
                    } catch (error) {
                        this.showLoadError();
                    }
                },
                
                // 解析数据
                parseData(rawData) {
                    try {
                        const data = JSON.parse(rawData);
                        
                        if (data.sorted_list && Array.isArray(data.sorted_list)) {
                            studentsData = data.sorted_list.map(student => {
                                // 统一字段名映射
                                const name = student.name || student.名称;
                                const genderRaw = student.gender || student.性别;
                                // 统一性别格式为英文
                                const gender = genderRaw === '男' ? 'male' : 
                                              genderRaw === '女' ? 'female' : 
                                              genderRaw === 'male' ? 'male' : 
                                              genderRaw === 'female' ? 'female' : 'male';
                                const score = student.score || student.分数;
                                const character = student.character || student.角色 || [];
                                const exceptional = student.exceptional_student || false;
                                
                                return {
                                    name,
                                    gender,
                                    genderDisplay: genderRaw === '男' || genderRaw === 'male' ? '男' : '女',
                                    score,
                                    character,
                                    exceptional
                                };
                            });
                            
                            // 处理分组数据，支持中文和英文字段名
                            groupsData = data.groups || data.群组 || [];
                            isDataLoaded = true;
                            
                            // 清除超时
                            if (loadTimeout) {
                                clearTimeout(loadTimeout);
                                loadTimeout = null;
                            }
                            
                            this.renderSeats();
                        } else {
                            throw new Error('数据格式不正确：缺少 sorted_list 字段');
                        }
                        
                    } catch (error) {
                        this.showLoadError();
                    }
                },
                
                // 显示加载错误 - Vue兼容版本
                showLoadError() {
                    window.vueCompatibleDomOperation(() => {
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (!grid) {
                            return;
                        }
                        
                        try {
                            grid.innerHTML = `
                                <div class="error-message">
                                    <h3>⚠️ 数据加载失败</h3>
                                    <p>未能获取到学生数据，可能的原因：</p>
                                    <ul>
                                        <li>后端数据尚未准备好</li>
                                        <li>数据格式不正确</li>
                                        <li>网络连接问题</li>
                                        <li>Vue渲染冲突</li>
                                    </ul>
                                    <div class="error-actions">
                                        <button class="seat-btn btn-primary" onclick="window.ensureGlobalFunctions && window.ensureGlobalFunctions(); window.SmartSeatSystem ? window.SmartSeatSystem.retryLoad() : window.handleSmartSeatAction('retryLoad')">🔄 重新加载</button>
                                        <button class="seat-btn btn-warning" onclick="location.reload()">🔄 刷新页面</button>
                                    </div>
                                </div>
                            `;
                        } catch (error) {
                            // 静默处理错误
                        }
                        
                        this.updateStats();
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 重新加载数据 - Vue兼容版本
                retryLoad() {
                    isDataLoaded = false;
                    loadAttempts = 0;
                    studentsData = [];
                    groupsData = [];
                    
                    window.vueCompatibleDomOperation(() => {
                        // 显示加载状态
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (grid) {
                            try {
                                grid.innerHTML = `
                                    <div class="loading">
                                        <div class="loading-spinner"></div>
                                        <div>正在重新加载数据...</div>
                                    </div>
                                `;
                            } catch (error) {
                                // 静默处理错误
                            }
                        }
                        
                        this.loadData();
                    }).catch(error => {
                        this.loadData(); // 尝试直接加载数据
                    });
                },
                


                
                // 安全的DOM操作 - Vue兼容增强版
                safeGetElement(id) {
                    try {
                        // 多重检查确保元素存在且有效
                        const element = document.getElementById(id);
                        if (!element) {
                            console.warn(`⚠️ 元素未找到: ${id}`);
                            return null;
                        }

                        if (!document.contains(element)) {
                            console.warn(`⚠️ 元素不在文档中: ${id}`);
                            return null;
                        }

                        // 检查元素是否被Vue管理
                        if (element.hasAttribute && (element.hasAttribute('data-v-') || element.closest('[data-v-]'))) {
                            console.log(`🔍 检测到Vue管理的元素: ${id}`);
                        }

                        return element;
                    } catch (error) {
                        console.error(`❌ safeGetElement 错误 (${id}):`, error);
                        return null;
                    }
                },

                // 安全的DOM查询 - Vue兼容增强版
                safeQuerySelectorAll(parent, selector) {
                    try {
                        if (!parent) {
                            console.warn(`⚠️ 父元素为空: ${selector}`);
                            return [];
                        }

                        if (!document.contains(parent)) {
                            console.warn(`⚠️ 父元素不在文档中: ${selector}`);
                            return [];
                        }

                        const elements = parent.querySelectorAll(selector);
                        if (!elements) {
                            console.warn(`⚠️ 查询结果为空: ${selector}`);
                            return [];
                        }

                        // 过滤掉已被移除的元素
                        const validElements = Array.from(elements).filter(el => {
                            return el && document.contains(el);
                        });

                        if (validElements.length !== elements.length) {
                            console.warn(`⚠️ 过滤了 ${elements.length - validElements.length} 个无效元素`);
                        }

                        return validElements;
                    } catch (error) {
                        console.error(`❌ safeQuerySelectorAll 错误 (${selector}):`, error);
                        return [];
                    }
                },

                // 安全的DOM移除操作 - Vue兼容
                safeRemoveChild(parent, child) {
                    try {
                        if (!parent || !child) {
                            return false;
                        }

                        if (!document.contains(parent) || !document.contains(child)) {
                            return false;
                        }

                        if (child.parentNode !== parent) {
                            console.warn('⚠️ 子元素的父节点不匹配，跳过移除');
                            return false;
                        }

                        parent.removeChild(child);
                        return true;
                    } catch (error) {
                        console.warn('⚠️ 安全移除子元素失败:', error);
                        return false;
                    }
                },

                // 渲染座位 - Vue兼容版本
                renderSeats() {
                    return window.vueCompatibleDomOperation(() => {
                        const grid = this.safeGetElement('smart-seat-seatGrid');
                        if (!grid) {
                            return;
                        }
                        
                        const totalSeats = config.rows * config.columns;
                        
                        try {
                            // 设置网格样式
                            grid.style.gridTemplateColumns = `repeat(${config.columns}, 1fr)`;
                            grid.style.gridTemplateRows = `repeat(${config.rows}, 1fr)`;
                            
                            // 最安全的清空方式 - Vue兼容增强版
                            let clearSuccess = false;

                            // 方法1：尝试使用innerHTML（最安全）
                            try {
                                grid.innerHTML = '';
                                clearSuccess = true;
                                console.log('✅ 使用innerHTML清空成功');
                            } catch (htmlError) {
                                console.warn('⚠️ innerHTML清空失败，尝试逐个移除:', htmlError);

                                // 方法2：安全移除子元素
                                const children = Array.from(grid.children || []);
                                let removedCount = 0;

                                children.forEach(child => {
                                    if (this.safeRemoveChild(grid, child)) {
                                        removedCount++;
                                    }
                                });

                                console.log(`🔄 逐个移除完成，成功移除 ${removedCount}/${children.length} 个元素`);
                                clearSuccess = removedCount === children.length;
                            }

                            // 方法3：如果前两种方法都失败，使用textContent清空
                            if (!clearSuccess && grid.children.length > 0) {
                                try {
                                    console.warn('⚠️ 前两种清空方法失败，使用textContent');
                                    grid.textContent = '';
                                    clearSuccess = true;
                                } catch (textError) {
                                    console.error('❌ 所有清空方法都失败:', textError);
                                }
                            }
                            
                            // 如果没有数据，显示加载状态
                            if (!isDataLoaded || studentsData.length === 0) {
                                for (let i = 0; i < totalSeats; i++) {
                                    const seat = this.createEmptySeat();
                                    if (seat && document.contains(grid)) {
                                        grid.appendChild(seat);
                                    }
                                }
                                this.updateStats();
                                return;
                            }
                            
                            // 按分组排列学生
                            const arrangedStudents = this.arrangeStudentsByGroups();
                            
                            // 生成座位 - Vue兼容版本
                            for (let i = 0; i < totalSeats; i++) {
                                try {
                                    const seat = i < arrangedStudents.length
                                        ? this.createStudentSeat(arrangedStudents[i], i)
                                        : this.createEmptySeat();

                                    if (seat && document.contains(grid)) {
                                        // 检查grid是否仍然有效
                                        if (grid.parentNode && document.contains(grid)) {
                                            grid.appendChild(seat);
                                        } else {
                                            console.warn('⚠️ 网格容器已失效，停止添加座位');
                                            break;
                                        }
                                    } else {
                                        console.warn(`⚠️ 座位 ${i} 创建失败或网格容器无效`);
                                    }
                                } catch (seatError) {
                                    console.error(`❌ 创建座位 ${i} 时出错:`, seatError);
                                    // 继续创建其他座位
                                }
                            }
                            
                            this.updateStats();
                        } catch (error) {
                            // 降级处理：使用innerHTML
                            grid.innerHTML = `
                                <div class="error-message">
                                    <h3>⚠️ 渲染出错</h3>
                                    <p>座位渲染时遇到问题，请刷新页面重试</p>
                                    <button class="seat-btn btn-primary" onclick="location.reload()">🔄 刷新页面</button>
                                </div>
                            `;
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 创建空座位
                createEmptySeat() {
                    const seat = document.createElement('div');
                    seat.className = 'seat empty';
                    seat.innerHTML = `
                        <div class="empty-seat-icon">🪑</div>
                        <div class="student-name">${isDataLoaded ? '空座位' : '等待数据'}</div>
                    `;
                    return seat;
                },
                
                // 创建学生座位 - Vue兼容增强版
                createStudentSeat(student, index) {
                    try {
                        const seat = document.createElement('div');
                        seat.className = `seat occupied ${student.gender}`;
                        seat.dataset.index = index;
                        seat.dataset.studentName = student.name;
                        seat.dataset.vueCompatible = 'true'; // 标记为Vue兼容

                        if (student.exceptional) {
                            seat.classList.add('exceptional');
                        }

                        // 获取分组信息
                        const groupInfo = this.getGroupInfo(student.name);
                        const characterText = Array.isArray(student.character)
                            ? student.character.slice(0, 2).join(' • ')
                            : student.character || '普通';

                        // 安全的HTML内容设置
                        try {
                            seat.innerHTML = `
                                <div class="student-avatar ${student.exceptional ? 'exceptional' : ''}">${student.name.charAt(0)}</div>
                                <div class="student-name">${student.name}</div>
                                <div class="student-info">${student.score}分 • ${characterText}</div>
                                <div class="status-indicator status-online" title="在线"></div>
                                ${groupInfo ? `<div class="indicator group-indicator" title="分组: ${groupInfo}">👥</div>` : ''}
                                ${student.exceptional ? `<div class="indicator exceptional-indicator" title="特殊学生">⚠️</div>` : ''}
                            `;
                        } catch (htmlError) {
                            console.error('❌ 设置座位HTML失败:', htmlError);
                            // 降级处理：使用textContent
                            seat.textContent = `${student.name} (${student.score}分)`;
                        }

                        // 添加点击事件 - Vue兼容版本
                        const clickHandler = (e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            // 延迟执行，避免与Vue事件冲突
                            setTimeout(() => {
                                try {
                                    this.showStudentDetails(student);
                                } catch (error) {
                                    console.error('❌ 显示学生详情失败:', error);
                                    alert(`学生信息：${student.name}\n成绩：${student.score}分`);
                                }
                            }, 10);
                        };

                        seat.addEventListener('click', clickHandler);

                        // 存储事件处理器引用，便于后续清理
                        seat._clickHandler = clickHandler;

                        return seat;
                    } catch (error) {
                        console.error('❌ 创建学生座位失败:', error);
                        // 返回一个简单的座位元素
                        const fallbackSeat = document.createElement('div');
                        fallbackSeat.className = 'seat occupied';
                        fallbackSeat.textContent = student.name || '未知学生';
                        return fallbackSeat;
                    }
                },
                
                // 按分组排列学生
                arrangeStudentsByGroups() {
                    const arranged = [];
                    const used = new Set();
                    
                    // 优先放置分组学生
                    for (const group of groupsData) {
                        for (const name of group) {
                            const student = studentsData.find(s => s.name === name);
                            if (student && !used.has(name)) {
                                arranged.push(student);
                                used.add(name);
                            }
                        }
                    }
                    
                    // 添加未分组学生
                    for (const student of studentsData) {
                        if (!used.has(student.name)) {
                            arranged.push(student);
                            used.add(student.name);
                        }
                    }
                    
                    return arranged;
                },
                
                // 获取分组信息
                getGroupInfo(studentName) {
                    for (const group of groupsData) {
                        if (group.includes(studentName)) {
                            return group.filter(name => name !== studentName).join(', ');
                        }
                    }
                    return null;
                },
                
                // 显示学生详情
                showStudentDetails(student) {
                    const groupInfo = this.getGroupInfo(student.name);
                    const characterText = Array.isArray(student.character) 
                        ? student.character.join(', ')
                        : student.character || '普通';
                    
                    const genderDisplay = student.genderDisplay || student.gender;
                    
                    const message = `
👤 学生信息详情

📝 姓名：${student.name}
${genderDisplay === '男' ? '👨' : '👩'} 性别：${genderDisplay}
📊 成绩：${student.score}分
🎭 性格：${characterText}
${student.exceptional ? '⚠️ 特殊学生：是' : '✅ 特殊学生：否'}
${groupInfo ? `👥 分组伙伴：${groupInfo}` : ''}

${student.exceptional ? '\n💡 建议：请给予特别关注和支持' : ''}
                    `.trim();
                    
                    alert(message);
                },
                
                // 更新统计信息 - Vue兼容版本
                updateStats() {
                    return window.vueCompatibleDomOperation(() => {
                        const totalSeats = config.rows * config.columns;
                        const occupiedSeats = studentsData.length;
                        const emptySeats = totalSeats - occupiedSeats;
                        
                        const elements = {
                            totalStudents: this.safeGetElement('smart-seat-totalStudents'),
                            occupiedSeats: this.safeGetElement('smart-seat-occupiedSeats'),
                            emptySeats: this.safeGetElement('smart-seat-emptySeats'),
                            dataStatus: this.safeGetElement('smart-seat-dataStatus')
                        };
                        
                        try {
                            if (elements.totalStudents) elements.totalStudents.textContent = studentsData.length;
                            if (elements.occupiedSeats) elements.occupiedSeats.textContent = occupiedSeats;
                            if (elements.emptySeats) elements.emptySeats.textContent = emptySeats;
                            if (elements.dataStatus) {
                                elements.dataStatus.textContent = isDataLoaded ? '已加载' : '等待中';
                            }
                        } catch (error) {
                            // 静默处理错误
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 重新排座
                shuffleSeats() {
                    if (!isDataLoaded) {
                        alert('⚠️ 请先加载数据');
                        return;
                    }
                    
                    if (studentsData.length === 0) {
                        alert('⚠️ 没有学生数据');
                        return;
                    }
                    
                    // 打乱分组顺序
                    for (let i = groupsData.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [groupsData[i], groupsData[j]] = [groupsData[j], groupsData[i]];
                    }
                    
                    this.renderSeats();
                },
                
                // 高亮分组 - Vue兼容版本
                highlightGroup() {
                    if (!isDataLoaded || groupsData.length === 0) {
                        alert('⚠️ 没有可用的分组数据');
                        return;
                    }
                    
                    window.vueCompatibleDomOperation(() => {
                        this.clearHighlights();
                        
                        const randomGroup = groupsData[Math.floor(Math.random() * groupsData.length)];
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        const seats = this.safeQuerySelectorAll(container, '.seat.occupied');
                        
                        seats.forEach(seat => {
                            try {
                                const studentName = seat.dataset.studentName;
                                if (randomGroup.includes(studentName)) {
                                    seat.classList.add('highlighted');
                                }
                            } catch (error) {
                                // 静默处理错误
                            }
                        });
                        
                        alert(`🎯 高亮分组：${randomGroup.join(' 和 ')}\n\n这些学生组成一个学习小组，建议加强协作！`);
                        
                        // 3秒后自动取消高亮
                        setTimeout(() => this.clearHighlights(), config.highlightDuration);
                    }).catch(error => {
                        alert('⚠️ 高亮分组功能暂时不可用');
                    });
                },

                
                // 清除高亮 - Vue兼容版本
                clearHighlights() {
                    try {
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        const highlightedSeats = this.safeQuerySelectorAll(container, '.seat.highlighted');
                        highlightedSeats.forEach(seat => {
                            try {
                                seat.classList.remove('highlighted');
                            } catch (error) {
                                // 静默处理错误
                            }
                        });
                        currentHighlighted = null;
                    } catch (error) {
                        // 静默处理错误
                    }
                },
                
                // 显示使用说明 - Vue兼容版本
                showInfo() {
                    window.vueCompatibleDomOperation(() => {
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.style.display = 'block';
                        } else {
                            alert('使用说明功能暂时不可用，请刷新页面重试');
                        }
                    }).catch(error => {
                        alert('使用说明功能暂时不可用');
                    });
                },
                
                // 关闭模态框 - Vue兼容版本
                closeModal() {
                    window.vueCompatibleDomOperation(() => {
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.style.display = 'none';
                        }
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 设置事件监听器 - Vue兼容版本
                setupEventListeners() {
                    window.vueCompatibleDomOperation(() => {
                        const container = this.safeGetElement('smart-seat-system');
                        if (!container) {
                            return;
                        }
                        
                        // 模态框外部点击关闭
                        const modal = this.safeGetElement('smart-seat-infoModal');
                        if (modal) {
                            modal.addEventListener('click', (e) => {
                                if (e.target.id === 'smart-seat-infoModal') {
                                    this.closeModal();
                                }
                            });
                        }
                        
                        // 键盘事件 - 只处理ESC键关闭模态框
                        document.addEventListener('keydown', (e) => {
                            if (e.key === 'Escape') {
                                // 只有当模态框显示时才关闭
                                const currentModal = this.safeGetElement('smart-seat-infoModal');
                                if (currentModal && currentModal.style.display === 'block') {
                                    this.closeModal();
                                    e.preventDefault(); // 只阻止ESC的默认行为
                                }
                            }
                        });
                    }).catch(error => {
                        // 静默处理错误
                    });
                },
                
                // 设置事件隔离 - 轻量级保护
                setupEventIsolation() {
                    // 事件保护已在Vue兼容操作中处理
                }
            };
            
                         // 防护措施：确保全局对象正确暴露
             if (typeof window !== 'undefined') {
                 window.SmartSeatSystem = SmartSeatSystem;
             }
            
                         // 初始化函数 - 支持多次调用
             function initializeSystem() {
                 try {
                     SmartSeatSystem.init();
                 } catch (error) {
                     setTimeout(initializeSystem, 1000); // 1秒后重试
                 }
             }
            
            // 多重初始化策略，应对Vue渲染
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeSystem);
            } else {
                // 立即执行
                initializeSystem();
            }
            
                         // 额外的延迟初始化，防止Vue渲染干扰
             setTimeout(() => {
                 if (!window.SmartSeatSystem || typeof window.SmartSeatSystem.showInfo !== 'function') {
                     window.SmartSeatSystem = SmartSeatSystem;
                     initializeSystem();
                 }
             }, 500);
             
             // Vue兼容的DOM变化监控器
             if (typeof MutationObserver !== 'undefined') {
                 const observer = new MutationObserver((mutations) => {
                     mutations.forEach((mutation) => {
                         if (mutation.type === 'childList') {
                             // 检查是否有组件被Vue重新渲染
                             const container = document.getElementById('smart-seat-system');
                             if (container && !container.dataset.vueCompatible) {
                                 console.log('🔄 检测到Vue重新渲染，重新初始化组件...');
                                 container.dataset.vueCompatible = 'true';
                                 
                                 // 确保全局对象可用
                                 window.SmartSeatSystem = SmartSeatSystem;
                                 window.vueCompatibleDomOperation = window.vueCompatibleDomOperation || function(op) {
                                     return new Promise((resolve, reject) => {
                                         setTimeout(() => {
                                             try { resolve(op()); } catch(e) { reject(e); }
                                         }, 0);
                                     });
                                 };
                                 window.handleSmartSeatAction = window.handleSmartSeatAction || function(action) {
                                     console.log('备用处理器:', action);
                                 };
                                 
                                 setTimeout(() => {
                                     try {
                                         initializeSystem();
                                     } catch (error) {
                                         console.error('❌ 重新初始化失败:', error);
                                     }
                                 }, 100);
                             }
                         }
                     });
                 });
                 
                 // 开始观察
                 setTimeout(() => {
                     const targetNode = document.body;
                     if (targetNode) {
                         observer.observe(targetNode, {
                             childList: true,
                             subtree: true,
                             attributes: false,
                             characterData: false
                         });
                         console.log('🔍 Vue兼容的DOM监控器已启动');
                     }
                 }, 1000);
             }
             
             // 增强的全局错误恢复机制
             window.addEventListener('error', (event) => {
                 const errorMessage = event.message || '';
                 const errorSource = event.filename || '';
                 const errorLine = event.lineno || 0;

                 console.warn('🚨 检测到全局错误:', {
                     message: errorMessage,
                     source: errorSource,
                     line: errorLine,
                     stack: event.error?.stack
                 });

                 // 检查是否是我们关心的错误类型
                 const isRelevantError = errorMessage.includes('querySelectorAll') ||
                                       errorMessage.includes('removeChild') ||
                                       errorMessage.includes('handleSmartSeatAction') ||
                                       errorMessage.includes('SmartSeatSystem') ||
                                       errorMessage.includes('smart-seat');

                 if (isRelevantError) {
                     console.log('🔧 启动错误恢复机制...');

                     // 延迟恢复，确保Vue操作完成
                     setTimeout(() => {
                         try {
                             // 重新确保全局函数可用
                             window.ensureGlobalFunctions();

                             // 重新设置主系统对象
                             if (window.SmartSeatSystem !== SmartSeatSystem) {
                                 window.SmartSeatSystem = SmartSeatSystem;
                                 console.log('🔄 重新设置SmartSeatSystem对象');
                             }

                             // 检查容器是否存在
                             const container = document.getElementById('smart-seat-system');
                             if (container) {
                                 if (!container.dataset.errorRecovery) {
                                     container.dataset.errorRecovery = 'true';
                                     console.log('✅ 容器标记为错误恢复状态');
                                 }
                             } else {
                                 console.warn('⚠️ 主容器丢失，可能被Vue重新渲染');
                             }

                             console.log('✅ 错误恢复完成');
                         } catch (recoveryError) {
                             console.error('❌ 错误恢复失败:', recoveryError);
                         }
                     }, 500);
                 }
             });

             // 未处理的Promise拒绝错误处理
             window.addEventListener('unhandledrejection', (event) => {
                 const reason = event.reason;
                 console.warn('🚨 未处理的Promise拒绝:', reason);

                 if (reason && typeof reason === 'object' && reason.message) {
                     const message = reason.message;
                     if (message.includes('smart-seat') || message.includes('SmartSeatSystem')) {
                         console.log('🔧 Promise错误恢复...');
                         event.preventDefault(); // 防止错误冒泡

                         setTimeout(() => {
                             window.ensureGlobalFunctions();
                         }, 100);
                     }
                 }
             });
             
             // 定期健康检查和自动恢复
             setInterval(() => {
                 try {
                     // 检查关键函数是否存在
                     if (!window.handleSmartSeatAction || !window.vueCompatibleDomOperation) {
                         window.ensureGlobalFunctions();
                     }
                     
                     // 检查主系统对象
                     if (!window.SmartSeatSystem || typeof window.SmartSeatSystem.showInfo !== 'function') {
                         if (typeof SmartSeatSystem !== 'undefined') {
                             window.SmartSeatSystem = SmartSeatSystem;
                         }
                     }
                 } catch (checkError) {
                     // 静默处理错误
                 }
             }, 5000); // 每5秒检查一次
             
             // Vue状态变化监听
             if (typeof MutationObserver !== 'undefined') {
                 const globalObserver = new MutationObserver((mutations) => {
                     let needsRecovery = false;
                     
                     mutations.forEach((mutation) => {
                         if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                             // 检查是否有我们的组件被移除
                             mutation.removedNodes.forEach((node) => {
                                 if (node.nodeType === 1 && (
                                     node.id === 'smart-seat-system' || 
                                     node.querySelector && node.querySelector('#smart-seat-system')
                                 )) {
                                     needsRecovery = true;
                                 }
                             });
                         }
                     });
                     
                     if (needsRecovery) {
                         setTimeout(() => {
                             window.ensureGlobalFunctions();
                             if (typeof SmartSeatSystem !== 'undefined') {
                                 window.SmartSeatSystem = SmartSeatSystem;
                             }
                         }, 1000);
                     }
                 });
                 
                 // 观察整个文档的变化
                 setTimeout(() => {
                     globalObserver.observe(document.body, {
                         childList: true,
                         subtree: true
                     });
                 }, 2000);
             }
            
        })();
    </script>
</div>
