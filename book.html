<!-- 学生成长手册组件 -->
<div id="student-handbook-container">
  <style>
    #student-handbook-container {
      font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
      color: #333;
      width: 100%;
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      box-sizing: border-box;
    }

    #student-handbook-container * {
      box-sizing: border-box;
    }

    #student-handbook-container .handbook-card {
      width: 100%;
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
      padding: 50px 40px 40px 40px;
      position: relative;
      overflow: hidden;
      border: none;
    }

    #student-handbook-container .handbook-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    #student-handbook-container .handbook-title {
      text-align: center;
      font-weight: 700;
      font-size: 32px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 35px;
      letter-spacing: 3px;
      position: relative;
    }

    #student-handbook-container .handbook-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 2px;
    }

    #student-handbook-container .handbook-info {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      font-size: 18px;
      margin-bottom: 15px;
      color: #555;
      background: #f8f9ff;
      padding: 20px;
      border-radius: 12px;
      border-left: 4px solid #667eea;
    }

    #student-handbook-container .handbook-info-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    #student-handbook-container .handbook-info-label {
      font-weight: 600;
      color: #667eea;
    }

    #student-handbook-container .handbook-score {
      font-size: 20px;
      color: #667eea;
      margin-bottom: 30px;
      margin-left: 2px;
      font-weight: 600;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    #student-handbook-container .handbook-content {
      text-indent: 2em;
      margin-bottom: 25px;
      font-size: 17px;
      color: #444;
      line-height: 2.2;
      white-space: pre-line;
      background: #fafbff;
      padding: 25px;
      border-radius: 12px;
      border-left: 4px solid #667eea;
      position: relative;
    }

    #student-handbook-container .handbook-extra {
      background: linear-gradient(135deg, #f0f6ff, #e8f2ff);
      border-left: 4px solid #667eea;
      border-radius: 12px;
      color: #667eea;
      font-size: 16px;
      margin-top: 20px;
      padding: 20px;
      text-indent: 2em;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
    }

    #student-handbook-container .handbook-btns {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 40px;
      flex-wrap: wrap;
    }

    #student-handbook-container .handbook-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      border: none;
      border-radius: 12px;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      min-width: 120px;
      position: relative;
      overflow: hidden;
      outline: none;
    }

    #student-handbook-container .handbook-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    #student-handbook-container .handbook-btn:hover::before {
      left: 100%;
    }

    #student-handbook-container .handbook-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    #student-handbook-container .handbook-btn.share {
      background: #fff;
      color: #667eea;
      border: 2px solid #667eea;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    }

    #student-handbook-container .handbook-btn.share:hover {
      background: #667eea;
      color: #fff;
    }

    #student-handbook-container .handbook-btn img {
      width: 20px;
      margin-right: 8px;
      filter: brightness(0) invert(1);
    }

    #student-handbook-container .handbook-btn.share img {
      filter: none;
    }

    #student-handbook-container .handbook-btn.share:hover img {
      filter: brightness(0) invert(1);
    }

    #student-handbook-container .handbook-success-message {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(400px);
      transition: transform 0.3s ease;
      z-index: 1000;
    }

    #student-handbook-container .handbook-success-message.show {
      transform: translateX(0);
    }

    @media (max-width: 768px) {
      #student-handbook-container {
        padding: 10px;
        margin: 10px auto;
      }

      #student-handbook-container .handbook-card {
        padding: 30px 20px;
      }

      #student-handbook-container .handbook-title {
        font-size: 24px;
      }

      #student-handbook-container .handbook-info {
        flex-direction: column;
        gap: 10px;
      }

      #student-handbook-container .handbook-btns {
        flex-direction: column;
        align-items: center;
      }

      #student-handbook-container .handbook-btn {
        width: 100%;
        max-width: 200px;
      }
    }

    @media print {
      #student-handbook-container .handbook-card {
        box-shadow: none !important;
        background: #fff !important;
        margin: 0 !important;
        padding: 20px !important;
        border-radius: 0 !important;
      }

      #student-handbook-container .handbook-btns { 
        display: none !important; 
      }

      #student-handbook-container .handbook-card::before {
        display: none !important;
      }
    }
  </style>

  <div class="handbook-card" id="handbookPrintArea">
    <div class="handbook-title">《学生成长手册》</div>
    <div class="handbook-info">
      <div class="handbook-info-item">
        <span class="handbook-info-label">班级：</span>
        <span>{{表单收集.class}}</span>
      </div>
      <div class="handbook-info-item">
        <span class="handbook-info-label">姓名：</span>
        <span>{{表单收集.name}}</span>
      </div>
    </div>
    <div class="handbook-score">
      成绩：{{表单收集.score}}
    </div>
    <div class="handbook-content">
      {{AI 对话.answer}}
    </div>

    <div class="handbook-btns">
      <button class="handbook-btn" onclick="printHandbookToPDF()">
        <img src="https://ai.jbangai.com/img/<EMAIL>" alt="PDF">导出PDF
      </button>
      <button class="handbook-btn" onclick="printHandbook()">
        <img src="https://ai.jbangai.com/img/<EMAIL>" alt="打印">打印
      </button>
      <button class="handbook-btn share" onclick="copyHandbookContent()">
        <img src="https://ai.jbangai.com/img/<EMAIL>" alt="分享">分享
      </button>
    </div>
  </div>

  <div class="handbook-success-message" id="handbookSuccessMessage">
    操作成功！
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script>
(function() {
  // 显示消息函数
  function showHandbookMessage(text) {
    const message = document.getElementById('handbookSuccessMessage');
    if (message) {
      message.textContent = text;
      message.classList.add('show');
      setTimeout(() => {
        message.classList.remove('show');
      }, 3000);
    }
  }

  // 复制内容函数
  window.copyHandbookContent = function() {
    const cardElement = document.querySelector('#student-handbook-container .handbook-card');
    if (cardElement) {
      const el = document.createElement('textarea');
      el.value = cardElement.innerText;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
      showHandbookMessage('内容已复制，可粘贴到其他地方！');
    }
  };

  // 打印函数
  window.printHandbook = function() {
    const originalContent = document.body.innerHTML;
    const handbookCard = document.querySelector('#student-handbook-container .handbook-card');
    
    if (handbookCard) {
      const printContent = handbookCard.cloneNode(true);
      // 移除按钮
      const buttons = printContent.querySelector('.handbook-btns');
      if (buttons) buttons.remove();
      
      document.body.innerHTML = printContent.outerHTML;
      window.print();
      document.body.innerHTML = originalContent;
      
      // 重新绑定事件
      setTimeout(() => {
        showHandbookMessage('打印任务已发送！');
      }, 100);
    }
  };

  // 导出PDF函数
  window.printHandbookToPDF = function() {
    const element = document.getElementById('handbookPrintArea');
    if (!element) return;

    const opt = {
      margin: [10, 10, 10, 10],
      filename: '学生成长手册.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { 
        scale: 2,
        useCORS: true,
        letterRendering: true
      },
      jsPDF: { 
        unit: 'mm', 
        format: 'a4', 
        orientation: 'portrait' 
      }
    };

    // 临时隐藏按钮
    const buttons = document.querySelector('#student-handbook-container .handbook-btns');
    const originalDisplay = buttons ? buttons.style.display : '';
    if (buttons) buttons.style.display = 'none';

    // 检查html2pdf是否可用
    if (typeof html2pdf !== 'undefined') {
      html2pdf().set(opt).from(element).save().then(() => {
        // 恢复按钮显示
        if (buttons) buttons.style.display = originalDisplay;
        showHandbookMessage('PDF导出成功！');
      }).catch(err => {
        console.error('PDF导出失败:', err);
        if (buttons) buttons.style.display = originalDisplay;
        showHandbookMessage('PDF导出失败，请重试');
      });
    } else {
      if (buttons) buttons.style.display = originalDisplay;
      showHandbookMessage('PDF库未加载，请刷新页面重试');
    }
  };
})();
</script>