<!-- 奖品刮刮乐组件 -->
<div id="scratch-game-container">
  <style>
    #scratch-game-container {
      font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      max-width: 700px;
      margin: 20px auto;
      box-sizing: border-box;
    }

    #scratch-game-container * {
      box-sizing: border-box;
    }

    #scratch-game-container .scratch-title {
      color: white;
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 30px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      text-align: center;
    }

    #scratch-game-container .scratch-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 15px;
      max-width: 500px;
      width: 100%;
      margin-bottom: 30px;
    }

    #scratch-game-container .scratch-card {
      width: 100px;
      height: 100px;
      position: relative;
      border: 2px solid #fff;
      border-radius: 12px;
      overflow: hidden;
      background: #fff;
      cursor: pointer;
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      user-select: none;
      transition: transform 0.2s ease;
      margin: 0 auto;
    }

    #scratch-game-container .scratch-card:hover {
      transform: translateY(-3px);
    }

    #scratch-game-container .scratch-cover {
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, 
        #c0c0c0 25%, 
        #d0d0d0 25%, 
        #d0d0d0 50%, 
        #c0c0c0 50%, 
        #c0c0c0 75%, 
        #d0d0d0 75%
      );
      background-size: 15px 15px;
      z-index: 2;
      transition: all 0.4s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
      line-height: 1.2;
    }

    #scratch-game-container .scratch-cover:hover {
      background: linear-gradient(45deg, 
        #b0b0b0 25%, 
        #c0c0c0 25%, 
        #c0c0c0 50%, 
        #b0b0b0 50%, 
        #b0b0b0 75%, 
        #c0c0c0 75%
      );
      background-size: 15px 15px;
    }

    #scratch-game-container .scratch-cover.hide {
      opacity: 0;
      transform: scale(0);
      pointer-events: none;
    }

    #scratch-game-container .prize-content {
      z-index: 1;
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: #333;
      text-align: center;
      padding: 8px;
      word-wrap: break-word;
      font-size: 12px;
      line-height: 1.2;
    }

    #scratch-game-container .scratch-reset-btn {
      padding: 10px 25px;
      background: linear-gradient(45deg, #ff6b6b, #ee5a24);
      color: white;
      border: none;
      border-radius: 20px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
      transition: all 0.3s ease;
      outline: none;
    }

    #scratch-game-container .scratch-reset-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(238, 90, 36, 0.6);
    }

    #scratch-game-container .scratch-reset-btn:active {
      transform: translateY(0);
    }

    @media (max-width: 768px) {
      #scratch-game-container {
        padding: 15px;
        margin: 10px;
      }
      
      #scratch-game-container .scratch-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        max-width: 300px;
      }
      
      #scratch-game-container .scratch-card {
        width: 80px;
        height: 80px;
        font-size: 12px;
      }
      
      #scratch-game-container .scratch-title {
        font-size: 22px;
        margin-bottom: 20px;
      }
    }
  </style>

  <h2 class="scratch-title">🎁 奖品刮刮乐 🎁</h2>
  <div class="scratch-grid" id="scratchGrid"></div>
  <button class="scratch-reset-btn" onclick="resetScratchGame()">重新开始</button>
</div>

<script>
(function() {
  // 奖品数据
  const scratchPrizes = [
    "{{global.object1}}",
    "{{global.object2}}", 
    "{{global.object3}}",
    "{{global.object4}}",
    "{{global.object5}}",
    "{{global.object6}}",
    "{{global.object7}}"
  ];

  // 洗牌算法
  function shufflePrizes(array) {
    let arr = array.slice();
    for (let i = arr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr;
  }

  // 渲染奖品卡片
  function renderScratchCards() {
    const container = document.getElementById('scratchGrid');
    if (!container) return;
    
    container.innerHTML = '';
    const shuffledPrizes = shufflePrizes(scratchPrizes);

    shuffledPrizes.forEach((prize, idx) => {
      const card = document.createElement('div');
      card.className = 'scratch-card';

      // 奖品内容
      const content = document.createElement('div');
      content.className = 'prize-content';
      content.innerHTML = prize;

      // 遮罩层
      const cover = document.createElement('div');
      cover.className = 'scratch-cover';
      cover.innerHTML = '点击<br>刮开';

      // 点击事件
      cover.addEventListener('click', function(e) {
        e.stopPropagation();
        cover.classList.add('hide');
        console.log(`刮开了奖品: ${prize}`);
      });

      card.appendChild(content);
      card.appendChild(cover);
      container.appendChild(card);
    });
  }

  // 重置游戏 - 全局函数
  window.resetScratchGame = function() {
    renderScratchCards();
  };

  // 初始化游戏
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', renderScratchCards);
  } else {
    renderScratchCards();
  }

  // 键盘快捷键
  document.addEventListener('keydown', function(e) {
    if ((e.key === 'r' || e.key === 'R') && e.target.closest('#scratch-game-container')) {
      resetScratchGame();
    }
  });
})();
</script>